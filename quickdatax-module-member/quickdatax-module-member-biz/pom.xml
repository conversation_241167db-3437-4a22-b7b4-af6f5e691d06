<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.quickdatax</groupId>
        <artifactId>quickdatax-module-member</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>quickdatax-module-member-biz</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        member 模块，我们放会员业务。
        例如说：会员中心等等
    </description>

    <dependencies>
        <dependency>
            <groupId>com.quickdatax</groupId>
            <artifactId>quickdatax-module-member-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.quickdatax</groupId>
            <artifactId>quickdatax-module-system-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.quickdatax</groupId>
            <artifactId>quickdatax-module-infra-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>com.quickdatax</groupId>
            <artifactId>quickdatax-spring-boot-starter-biz-tenant</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>com.quickdatax</groupId>
            <artifactId>quickdatax-spring-boot-starter-security</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.quickdatax</groupId>
            <artifactId>quickdatax-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.quickdatax</groupId>
            <artifactId>quickdatax-spring-boot-starter-redis</artifactId>
        </dependency>

        <!-- 消息队列相关 -->
        <dependency>
            <groupId>com.quickdatax</groupId>
            <artifactId>quickdatax-spring-boot-starter-mq</artifactId>
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>com.quickdatax</groupId>
            <artifactId>quickdatax-spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>com.quickdatax</groupId>
            <artifactId>quickdatax-spring-boot-starter-excel</artifactId>
        </dependency>

        <dependency>
            <groupId>com.quickdatax</groupId>
            <artifactId>quickdatax-spring-boot-starter-biz-ip</artifactId>
        </dependency>

    </dependencies>

</project>
