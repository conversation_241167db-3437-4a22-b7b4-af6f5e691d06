package com.quickdatax.module.bms.controller.admin.customer;

import com.quickdatax.framework.common.pojo.CommonResult;
import com.quickdatax.module.bms.controller.admin.customer.vo.CustomerSaveReqVO;
import com.quickdatax.module.bms.service.CustomerService;
import com.quickdatax.module.bms.service.doservice.customer.MCustomerMemberService;
import com.quickdatax.module.bms.service.doservice.customer.MCustomerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

import static com.quickdatax.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @date 2024/08/12
 */
@Tag(name = "运营后台 - 客户信息")
@Validated
@RestController
@RequestMapping("/bms/customer")
@RequiredArgsConstructor
public class CustomerController {

    private final MCustomerService mCustomerService;
    private final MCustomerMemberService mCustomerMemberService;
    private final CustomerService customerService;


    @PostMapping("/create")
    @Operation(summary = "创建客户信息")
    @PreAuthorize("@ss.hasPermission('bms:customer:create')")
    public CommonResult<Integer> createCustomer(@Valid @RequestBody CustomerSaveReqVO createReqVO) {
        return success(customerService.createCustomer(createReqVO).getId());
    }
/*
    @PutMapping("/update")
    @Operation(summary = "更新客户信息")
    @PreAuthorize("@ss.hasPermission('bms:customer:update')")
    public CommonResult<Boolean> updateCustomer(@Valid @RequestBody CustomerSaveReqVO updateReqVO) {
        MCustomerService.updateCustomer(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除客户信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('bms:customer:delete')")
    public CommonResult<Boolean> deleteCustomer(@RequestParam("id") Integer id) {
        MCustomerService.deleteCustomer(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得客户信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('bms:customer:query')")
    public CommonResult<CustomerRespVO> getCustomer(@RequestParam("id") Integer id) {
        MCustomerDO customer = MCustomerService.getCustomer(id);
        return success(BeanUtils.toBean(customer, CustomerRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得客户信息分页")
    @PreAuthorize("@ss.hasPermission('bms:customer:query')")
    public CommonResult<PageResult<CustomerRespVO>> getCustomerPage(@Valid CustomerPageReqVO pageReqVO) {
        PageResult<MCustomerDO> pageResult = MCustomerService.getCustomerPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CustomerRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出客户信息 Excel")
    @PreAuthorize("@ss.hasPermission('bms:customer:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportCustomerExcel(@Valid CustomerPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<MCustomerDO> list = MCustomerService.getCustomerPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "客户信息.xls", "数据", CustomerRespVO.class,
                        BeanUtils.toBean(list, CustomerRespVO.class));
    }

    // ==================== 子表（客户终端） ====================

    @GetMapping("/customer-terminal/page")
    @Operation(summary = "获得客户终端分页")
    @Parameter(name = "customerId", description = "客户id")
    @PreAuthorize("@ss.hasPermission('bms:customer:query')")
    public CommonResult<PageResult<MCustomerTerminalDO>> getCustomerTerminalPage(PageParam pageReqVO,
                                                                                        @RequestParam("customerId") Integer customerId) {
        return success(MCustomerService.getCustomerTerminalPage(pageReqVO, customerId));
    }

    @PostMapping("/customer-terminal/create")
    @Operation(summary = "创建客户终端")
    @PreAuthorize("@ss.hasPermission('bms:customer:create')")
    public CommonResult<Integer> createCustomerTerminal(@Valid @RequestBody MCustomerTerminalDO customerTerminal) {
        return success(MCustomerService.createCustomerTerminal(customerTerminal));
    }

    @PutMapping("/customer-terminal/update")
    @Operation(summary = "更新客户终端")
    @PreAuthorize("@ss.hasPermission('bms:customer:update')")
    public CommonResult<Boolean> updateCustomerTerminal(@Valid @RequestBody MCustomerTerminalDO customerTerminal) {
        MCustomerService.updateCustomerTerminal(customerTerminal);
        return success(true);
    }

    @DeleteMapping("/customer-terminal/delete")
    @Parameter(name = "id", description = "编号", required = true)
    @Operation(summary = "删除客户终端")
    @PreAuthorize("@ss.hasPermission('bms:customer:delete')")
    public CommonResult<Boolean> deleteCustomerTerminal(@RequestParam("id") Integer id) {
        MCustomerService.deleteCustomerTerminal(id);
        return success(true);
    }

	@GetMapping("/customer-terminal/get")
	@Operation(summary = "获得客户终端")
	@Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('bms:customer:query')")
	public CommonResult<MCustomerTerminalDO> getCustomerTerminal(@RequestParam("id") Integer id) {
	    return success(MCustomerService.getCustomerTerminal(id));
	}

    // ==================== 子表（客户成员） ====================

    @GetMapping("/customer-member/page")
    @Operation(summary = "获得客户成员分页")
    @Parameter(name = "customerId", description = "客户id")
    @PreAuthorize("@ss.hasPermission('bms:customer:query')")
    public CommonResult<PageResult<MCustomerMemberDO>> getCustomerMemberPage(PageParam pageReqVO,
                                                                                        @RequestParam("customerId") Integer customerId) {
        return success(MCustomerService.getCustomerMemberPage(pageReqVO, customerId));
    }

    @PostMapping("/customer-member/create")
    @Operation(summary = "创建客户成员")
    @PreAuthorize("@ss.hasPermission('bms:customer:create')")
    public CommonResult<Integer> createCustomerMember(@Valid @RequestBody CustomerMemberSaveReqVO customerMember) {
        return success(MCustomerMemberService.createCustomerMember(customerMember));
    }

    @PutMapping("/customer-member/update")
    @Operation(summary = "更新客户成员")
    @PreAuthorize("@ss.hasPermission('bms:customer:update')")
    public CommonResult<Boolean> updateCustomerMember(@Valid @RequestBody MCustomerMemberDO customerMember) {
        MCustomerService.updateCustomerMember(customerMember);
        return success(true);
    }

    @DeleteMapping("/customer-member/delete")
    @Parameter(name = "id", description = "编号", required = true)
    @Operation(summary = "删除客户成员")
    @PreAuthorize("@ss.hasPermission('bms:customer:delete')")
    public CommonResult<Boolean> deleteCustomerMember(@RequestParam("id") Integer id) {
        MCustomerService.deleteCustomerMember(id);
        return success(true);
    }

	@GetMapping("/customer-member/get")
	@Operation(summary = "获得客户成员")
	@Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('bms:customer:query')")
	public CommonResult<MCustomerMemberDO> getCustomerMember(@RequestParam("id") Integer id) {
	    return success(MCustomerService.getCustomerMember(id));
	}

    // ==================== 子表（客户数据库） ====================

    @GetMapping("/customer-datasource/page")
    @Operation(summary = "获得客户数据库分页")
    @Parameter(name = "customerId", description = "客户id")
    @PreAuthorize("@ss.hasPermission('bms:customer:query')")
    public CommonResult<PageResult<MCustomerDatasourceDO>> getCustomerDatasourcePage(PageParam pageReqVO,
                                                                                        @RequestParam("customerId") Integer customerId) {
        return success(MCustomerService.getCustomerDatasourcePage(pageReqVO, customerId));
    }

    @PostMapping("/customer-datasource/create")
    @Operation(summary = "创建客户数据库")
    @PreAuthorize("@ss.hasPermission('bms:customer:create')")
    public CommonResult<Integer> createCustomerDatasource(@Valid @RequestBody MCustomerDatasourceDO customerDatasource) {
        return success(MCustomerService.createCustomerDatasource(customerDatasource));
    }

    @PutMapping("/customer-datasource/update")
    @Operation(summary = "更新客户数据库")
    @PreAuthorize("@ss.hasPermission('bms:customer:update')")
    public CommonResult<Boolean> updateCustomerDatasource(@Valid @RequestBody MCustomerDatasourceDO customerDatasource) {
        MCustomerService.updateCustomerDatasource(customerDatasource);
        return success(true);
    }

    @DeleteMapping("/customer-datasource/delete")
    @Parameter(name = "id", description = "编号", required = true)
    @Operation(summary = "删除客户数据库")
    @PreAuthorize("@ss.hasPermission('bms:customer:delete')")
    public CommonResult<Boolean> deleteCustomerDatasource(@RequestParam("id") Integer id) {
        MCustomerService.deleteCustomerDatasource(id);
        return success(true);
    }

	@GetMapping("/customer-datasource/get")
	@Operation(summary = "获得客户数据库")
	@Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('bms:customer:query')")
	public CommonResult<MCustomerDatasourceDO> getCustomerDatasource(@RequestParam("id") Integer id) {
	    return success(MCustomerService.getCustomerDatasource(id));
	} */

}