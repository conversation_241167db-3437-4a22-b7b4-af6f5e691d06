package com.quickdatax.module.bms.controller.app.machine.vo;

import com.quickdatax.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/08/03
 */
@Data
@Schema(description = "设备列表请求参数")
public class AppMachinePageReqVO extends PageParam {
    @Schema(description = "设备id")
    private Integer machineId;
    @Schema(description = "设备名称")
    private String machineName;
    @Schema(description = "是否已绑定终端")
    private Boolean binding;
}
