package com.quickdatax.module.bms.controller.pc.machine.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/08/03
 */
@Schema(description = "PC - 设备分组新增/修改 Request VO")
@Data
public class PcMachineGroupSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "16428")
    private Integer id;

    @Schema(description = "分组名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "分组名称", maxLength = 10)
    @NotEmpty(message = "分组名称不能为空")
    private String name;

    // @Schema(description = "分组标签Id;产品分组展示用")
    // private Integer tagId;

    @Schema(description = "上级分组id", example = "0")
    private Integer parentId;

    @Schema(description = "是否可以添加设备", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否可以添加设备不能为空")
    private Boolean machineAdd;

}