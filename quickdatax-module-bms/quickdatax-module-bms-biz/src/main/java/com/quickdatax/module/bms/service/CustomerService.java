package com.quickdatax.module.bms.service;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.quickdatax.framework.common.enums.UserTypeEnum;
import com.quickdatax.framework.common.enums.WhetherConstant;
import com.quickdatax.framework.common.exception.util.CheckUtil;
import com.quickdatax.framework.common.pojo.PageParam;
import com.quickdatax.framework.common.pojo.PageResult;
import com.quickdatax.framework.common.util.servlet.ServletUtils;
import com.quickdatax.framework.security.core.util.CustomerSecurityFrameworkUtils;
import com.quickdatax.framework.tenant.core.context.CustomerContextHolder;
import com.quickdatax.framework.tenant.core.db.dynamic.CustomerDsProcessor;
import com.quickdatax.module.bms.controller.admin.customer.vo.CustomerSaveReqVO;
import com.quickdatax.module.bms.controller.admin.customer.vo.CustomerSaveRespVO;
import com.quickdatax.module.bms.controller.app.member.vo.*;
import com.quickdatax.module.bms.controller.pc.machine.vo.PcStaffAuthorizeMachineRespVO;
import com.quickdatax.module.bms.controller.pc.member.vo.PcCustomerDatasourceParams;
import com.quickdatax.module.bms.convert.UserConvert;
import com.quickdatax.module.bms.dal.dataobject.customer.MCustomerDO;
import com.quickdatax.module.bms.dal.dataobject.customer.MCustomerDatasourceDO;
import com.quickdatax.module.bms.dal.dataobject.customer.MCustomerMemberDO;
import com.quickdatax.module.bms.dal.dataobject.machine.CMachineDO;
import com.quickdatax.module.bms.dal.dataobject.machine.CMachineGroupDO;
import com.quickdatax.module.bms.dal.dataobject.machine.CMemberMachineDO;
import com.quickdatax.module.bms.enums.ErrorCodeConstants;
import com.quickdatax.module.bms.service.doservice.customer.MCustomerDatasourceService;
import com.quickdatax.module.bms.service.doservice.customer.MCustomerMemberService;
import com.quickdatax.module.bms.service.doservice.customer.MCustomerService;
import com.quickdatax.module.bms.service.doservice.machine.*;
import com.quickdatax.module.infra.api.db.config.ConfigServiceApi;
import com.quickdatax.module.system.api.oauth2.OAuth2TokenApi;
import com.quickdatax.module.system.api.sms.SmsCodeApi;
import com.quickdatax.module.system.api.sms.dto.code.SmsCodeUseReqDTO;
import com.quickdatax.module.system.enums.oauth2.OAuth2ClientConstants;
import com.quickdatax.module.system.enums.sms.SmsSceneEnum;
import com.quickdatax.module.system.enums.social.SocialTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.quickdatax.module.bms.enums.ErrorCodeConstants.ADMIN_TO_SELF_NOT_ALLOWED;
import static com.quickdatax.module.bms.service.CustomerMemberAuthService.DEMO_ACCOUNT;
import static com.quickdatax.module.bms.service.CustomerMemberAuthService.SMS_CODE_KEY;

/**
 * <AUTHOR>
 * @date 2024/08/12
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerService {
    private final MCustomerMemberService mCustomerMemberService;
    private final CMemberMachineService cMemberMachineService;
    private final CustomerUserService customerUserService;
    private final CMachineService cMachineService;
    private final CMachineGroupService cMachineGroupService;
    private final MCustomerService mCustomerService;
    private final MCustomerDatasourceService mCustomerDatasourceService;
    private final CustomerDsProcessor customerDsProcessor;
    private final SmsCodeApi smsCodeApi;
    private final ConfigServiceApi configServiceApi;
    private final OAuth2TokenApi oAuth2TokenApi;
    private final CMachineGroupTagService cMachineGroupTagService;
    private final CMachineGroupRelationService cMachineGroupRelationService;



    @DSTransactional
    public void initCustomerData(@NotNull Integer customerId) {
        Integer oldCustomerId = CustomerContextHolder.getCustomerId();
        try {
            CustomerContextHolder.setCustomerId(customerId);
            // 创建默认车间、分组标签
            Integer groupTagId = cMachineGroupTagService.initDefaultGroupTag();
            cMachineGroupService.initDefaultGroup(groupTagId);
        } finally {
            CustomerContextHolder.setCustomerId(oldCustomerId);
        }
    }

    /**
     * <p>
     * 变更管理员
     * </p>
     */
    @DSTransactional
    public void changeAdmin(@NotBlank String phone, @NotBlank String code, @NotNull Integer smsScene) {
        // 校验新的手机号是否已存在平台
        Integer customerId = CustomerSecurityFrameworkUtils.getCustomerId();
        MCustomerMemberDO newCustomerAdmin = mCustomerMemberService.getCustomerMemberByPhone(phone);
        CheckUtil.checkNot(null != newCustomerAdmin && !customerId.equals(newCustomerAdmin.getCustomerId()), ErrorCodeConstants.CUSTOMER_ADMIN_JOIN_OTHER_COMPANY);

        String defaultCode = configServiceApi.getConfigValue(SMS_CODE_KEY);
        if (!DEMO_ACCOUNT.equals(phone) && !code.equals(defaultCode)) {
            smsCodeApi.useSmsCode(new SmsCodeUseReqDTO(phone, smsScene, code, ServletUtils.getClientIP()));
        }

        // 移除新管理员原有配置
        if (null != newCustomerAdmin) {
            CheckUtil.checkNot(newCustomerAdmin.getId().equals(CustomerSecurityFrameworkUtils.getMemberId()),
                    ADMIN_TO_SELF_NOT_ALLOWED);
            customerUserService.removeMember(newCustomerAdmin.getId(), newCustomerAdmin.getCustomerId());
        }

        // 删除旧管理员的移动端和pc端token
        Integer loginUserId = CustomerSecurityFrameworkUtils.getMemberId();
        assert null != loginUserId;
        removeUserAllClientAccessToken(loginUserId);

        // 移除旧管理员的社交绑定关系
        customerUserService.unbindSocial(loginUserId, SocialTypeEnum.WECHAT_MINI_APP);

        // 修改旧管理员的手机号为新管理员的手机号
        mCustomerMemberService.changeAdminPhone(loginUserId, phone);
        // 修改客户信息
        mCustomerService.changeAdmin(customerId, phone);
    }

    public void removeUserAllClientAccessToken(@NotNull Integer userId) {
        oAuth2TokenApi.removeAccessToken(userId, UserTypeEnum.CUSTOMER_ADMIN.getValue(), OAuth2ClientConstants.MOBILE);
        oAuth2TokenApi.removeAccessToken(userId, UserTypeEnum.MEMBER.getValue(), OAuth2ClientConstants.MOBILE);
        oAuth2TokenApi.removeAccessToken(userId, UserTypeEnum.CUSTOMER_ADMIN.getValue(), OAuth2ClientConstants.PC);
    }

    /**
     * 新增客户
     * 1. 客户表新增信息
     * 2. 客户员工表新增管理员信息
     * 3. 新增数据源配置信息
     */
    @DSTransactional
    public CustomerSaveRespVO createCustomer(CustomerSaveReqVO reqVO) {
        // 客户基本信息
        Integer customerId = mCustomerService.createCustomer(reqVO.setId(null));
        // 管理员信息
        Integer customerAdminId = mCustomerMemberService.addCustomerAdmin(customerId, reqVO.getPhone());

        // 创建数据源连接配置
        String schema = MCustomerDatasourceDO.SCHEMA_PREFIX + customerId;
        String username = MCustomerDatasourceDO.USERNAME_PREFIX + customerId;
        String password = RandomUtil.randomString(MCustomerDatasourceDO.PASSWORD_LENGTH);
        PcCustomerDatasourceParams params = PcCustomerDatasourceParams.builder().customerId(customerId).schema(schema).username(username).password(password).build();
        mCustomerDatasourceService.createCustomerDatasource(params);

        // 创建数据库用户
        mCustomerDatasourceService.createPgsqlUser(params);
        // 初始化客户的数据源连接
        try {
            customerDsProcessor.initDatasource(customerId);
        } catch (Exception e) {
            // 数据源补偿措施
            mCustomerDatasourceService.removePgsqlUser(params);
        }
        return new CustomerSaveRespVO().setAdminId(customerAdminId).setId(customerId);
    }

    @DSTransactional
    public void encrypt(AppEncryptReqVO reqVO, Integer customerId) {
        MCustomerDO customer = mCustomerService.getCustomer(customerId);
        if (reqVO.getEncrypt().equals(customer.getEncrypted())) {
            CheckUtil.checkNot(WhetherConstant.YES.equals(customer.getEncrypted()), ErrorCodeConstants.ENCRYPTED_REPEAT);
            CheckUtil.checkNot(WhetherConstant.NO.equals(customer.getEncrypted()), ErrorCodeConstants.CANCEL_ENCRYPTED_REPEAT);
        }
        // 批量修改设备名称
        cMachineService.batchUpdateMachineName(reqVO.getMachineEncryptInfo());
        // 批量修改分组名称
        cMachineGroupService.batchUpdateGroupName(reqVO.getGroupEncryptInfo());
        // 修改客户的加密状态
        mCustomerService.updateEncryptInfo(reqVO.getEncrypt(), reqVO.getEncryptString(), customerId);

    }

    /**
     * <p>
     *  重置加密密码
     * </p>
     * @param reqVO
     * @param userId
     * @return
     */
    @DSTransactional
    public void resetEncrypt(AppResetEncryptPasswordVO reqVO, Integer userId) {
        // 校验手机号是否登录的用户
        MCustomerMemberDO adminInfo = mCustomerMemberService.getCustomerMember(userId);
        CheckUtil.check(adminInfo.getMobile().equals(reqVO.getMobile()), ErrorCodeConstants.PERMISSION_DENIED);
        // 使用验证码
        SmsCodeUseReqDTO smsCodeUseReqDTO = SmsCodeUseReqDTO.builder().usedIp(ServletUtils.getClientIP()).code(reqVO.getCode())
                .scene(SmsSceneEnum.CUSTOMER_ADMIN_RESET_ENCRYPT_PASSWORD.getScene()).mobile(reqVO.getMobile()).build();
        smsCodeApi.useSmsCode(smsCodeUseReqDTO);
        // 批量重置设备名称
        cMachineService.lambdaUpdate().set(CMachineDO::getName, "***").update();
        // 批量重置分组名称
        cMachineGroupService.lambdaUpdate().set(CMachineGroupDO::getName, "***").update();
        // 修改客户的加密状态
        mCustomerService.updateEncryptInfo(WhetherConstant.NO, null, userId);
    }

    public PageResult<AppCustomerMemberRespVO> getCustomerMember(Integer customerId, PageParam pageReqVO) {
        // 分页获取客户员工
        PageResult<MCustomerMemberDO> memberPage = mCustomerMemberService.getCustomerMemberPage(customerId, pageReqVO);
        // 获取员工设备数量
        List<Integer> memberIds = memberPage.getList().stream().map(MCustomerMemberDO::getId).collect(Collectors.toList());
        Map<Integer, Long> memberMachineCount = cMemberMachineService.getMemberMachineCount(memberIds);

        List<AppCustomerMemberRespVO> res = memberPage.getList().stream()
            .map(memberDO -> UserConvert.INSTANCE.convert(memberDO, memberMachineCount.getOrDefault(memberDO.getId(), 0L)))
            .collect(Collectors.toList());

        return new PageResult<>(res, memberPage.getTotal());
    }

    public List<AppStaffAuthorizeMachineRespVO> getStaffMachine(Integer memberId) {
        List<AppGroupMachineRespVO> allGroupMachine = customerUserService.getMachineInfoGroupByRootGroup(CustomerSecurityFrameworkUtils.getMemberId());
        Set<Integer> memberMachineIds = cMemberMachineService.getMemberMachines(memberId).stream().map(CMemberMachineDO::getMachineId)
                .collect(Collectors.toSet());

        return allGroupMachine.stream().map(groupMachine -> {
            AppStaffAuthorizeMachineRespVO entity = new AppStaffAuthorizeMachineRespVO();
            entity.setGroupId(groupMachine.getGroupId());
            entity.setGroup(groupMachine.getGroupName());
            entity.setAuthorizedMachine(groupMachine.getMachines(), memberMachineIds);
            return entity;
        }).collect(Collectors.toList());
    }

    public PcStaffAuthorizeMachineRespVO getPcStaffMachineTree(Integer memberId, Integer customerId) {
        // 所有分组的树节点（包括客户）
        List<TreeNode<Integer>> allGroupTreeNode = cMachineGroupService.getAllGroupTreeNode().stream()
                .peek(node -> {
                    node.setId(-node.getId());
                    node.setParentId(-node.getParentId());
                })
                .collect(Collectors.toList());
        // 添加客户信息节点
        allGroupTreeNode.add(mCustomerService.getCustomerNameTreeNode(customerId));

        // 所有设备的树节点
        List<TreeNode<Integer>> allMachineTreeNode = buildMachineTreeNode().stream().peek(node -> {
            node.setParentId(-node.getParentId());
        }).collect(Collectors.toList());

        // 员工拥有的设备数
        Set<Integer> memberMachineIds = cMemberMachineService.getMemberMachines(memberId).stream().map(CMemberMachineDO::getMachineId)
            .collect(Collectors.toSet());

        allGroupTreeNode.addAll(allMachineTreeNode);
        List<Tree<Integer>> tree = TreeUtil.build(allGroupTreeNode, Integer.MIN_VALUE);

        return PcStaffAuthorizeMachineRespVO.builder().machineIds(memberMachineIds).machineGroupNode(tree).build();
    }

    /**
     * 构造设备树节点
     * @return {@code List<TreeNode<Integer>> }
     */
    private List<TreeNode<Integer>> buildMachineTreeNode() {
        ArrayList<TreeNode<Integer>> res = new ArrayList<>();

        cMachineService.list().forEach(machine -> {
            List<Integer> groupIds = cMachineGroupRelationService.getGroupIdsByMachineId(machine.getId());
            groupIds.forEach(groupId -> {
                TreeNode<Integer> treeNode = new TreeNode<>();
                treeNode.setId(machine.getId());
                treeNode.setParentId(groupId);
                treeNode.setName(machine.getName());
                res.add(treeNode);
            });
        });
        return res;
    }

    // public PcStaffAuthorizeMachineRespVO getPcStaffMachine(Integer memberId) {
    //     List<TreeNode<Integer>> allGroupTreeNode = cMachineGroupService.getAllGroupTreeNode();
    //     Map<Integer, List<CMachineDO>> machineGroupByGroupId = cMachineService.list().stream().collect(Collectors.groupingBy(CMachineDO::getGroupId));
    //
    //     Set<Integer> memberMachineIds = cMemberMachineService.getMemberMachines(memberId).stream().map(CMemberMachineDO::getMachineId)
    //             .collect(Collectors.toSet());
    //
    //     allGroupTreeNode.forEach(groupTreeNode -> {
    //         Integer groupId = groupTreeNode.getId();
    //         // 获取分组的所有设备
    //         List<CMachineDO> groupMachines = machineGroupByGroupId.getOrDefault(groupId, Collections.emptyList());
    //         List<PcTreeMachineInfoVO> machineInfo = groupMachines.stream().map(machine -> PcTreeMachineInfoVO.builder().machineId(machine.getId()).machineName(machine.getName()).build()).collect(Collectors.toList());
    //         groupTreeNode.setExtra(MapUtil.of("machineList", machineInfo));
    //     });
    //
    //     return PcStaffAuthorizeMachineRespVO.builder().machineIds(memberMachineIds).machineGroupNode(TreeUtil.build(allGroupTreeNode)).build();
    // }

    public void authorizeMachine(Integer memberId, List<Integer> machineIds) {

        if (CollectionUtils.isNotEmpty(machineIds)) {
            machineIds = machineIds.stream().distinct().collect(Collectors.toList());
        }
        cMachineService.checkMachineExist(machineIds);

        // 授权设备
        cMemberMachineService.authorizeMachine(memberId, machineIds);
    }

    private CustomerService getSelf() {
        return SpringUtil.getBean(getClass());
    }

}
