package com.quickdatax.module.bms.controller.admin.machine.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 设备监测指标新增/修改 Request VO")
@Data
public class MachineMonitorSaveReqVO {

    @Schema(description = "id;主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "31453")
    private Integer id;

    @Schema(description = "设备id", requiredMode = Schema.RequiredMode.REQUIRED, example = "3493")
    @NotNull(message = "设备id不能为空")
    private Integer machineId;

    @Schema(description = "终端id", requiredMode = Schema.RequiredMode.REQUIRED, example = "10998")
    @NotNull(message = "终端id不能为空")
    private Integer terminalId;

    @Schema(description = "终端token", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "终端token不能为空")
    private String clientToken;

    @Schema(description = "名字;客户可自定义修改", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @NotEmpty(message = "名字;客户可自定义修改不能为空")
    private String name;

    @Schema(description = "类型;1：产能 2:485", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "类型;1：产能 2:485不能为空")
    private Short type;

    @Schema(description = "地址")
    private Integer address;

    @Schema(description = "波特率")
    private Integer baudrate;

    @Schema(description = "校验位;校验，无校验：0，奇校验：1，偶校验：2")
    private Integer parityBit;

    @Schema(description = "数据位")
    private Integer dataBit;

    @Schema(description = "停止位")
    private Integer stopBit;

    @Schema(description = "功能码;取值范围 1(读从机线圈寄存器，位操作) 2（读离散输入寄存器，位操作） 3（读保持寄存器）4（读输入寄存器）")
    private Integer funcode;

    @Schema(description = "数据类型;取值范围 1（16位有符号数） 2（16位无符号数）3 （32位有符号数） 4（32位无符号数）5（32位浮点型）", example = "2")
    private Integer datatype;

    @Schema(description = "寄存器地址;取值范围 1-65535 必选参数，寄存器地址表示的是数据地址。如果数据地址是16进制表示（0x0000开始），则需要把16进制转为十进制并加1，才是寄存器地址的值。如果地址使用区号+序号表示（比如40001），则寄存器地址只取序号就可以了（也就是1）")
    private Integer reg;

    @Schema(description = "单位;例如：功率的KW、电量的“度”")
    private String unit;

    @Schema(description = "换算系数;传感器采集的数据需要*factor才是正确的值")
    private BigDecimal factor;

    @Schema(description = "标准值")
    private Short standardValue;

}