#########################################
# QuickDataX 项目 ProGuard 混淆配置文件
# 适用于基于Spring Boot的企业级管理系统
#########################################

#########################################
# 基础配置
#########################################

-dontwarn
-ignorewarnings
-dontoptimize         # 禁用优化，避免反射问题
-dontpreverify
-keepattributes *Annotation*,Signature,InnerClasses,EnclosingMethod,Exceptions,LineNumberTable,SourceFile
-dontshrink           # 禁用代码压缩，保证稳定性

# 保留泛型信息
-keepattributes Signature
# 保留异常信息
-keepattributes Exceptions
# 保留源文件和行号信息（用于调试）
-keepattributes SourceFile,LineNumberTable

#########################################
# 保留入口类（Spring Boot 启动类）
#########################################

# 保留服务端启动类
-keep class com.quickdatax.server.QuickdataxServerApplication {
    public static void main(java.lang.String[]);
}

# 保留客户端启动类
-keep class com.quickdatax.client.QuickdataxClientApplication {
    public static void main(java.lang.String[]);
}

# 保留开放API启动类（如果存在）
-keep class com.quickdatax.openapi.** {
    public static void main(java.lang.String[]);
}

#########################################
# Spring 框架相关配置
#########################################

# 保留所有注解类本身
-keep @interface *

# 保留Spring核心注解标注的类
-keep @org.springframework.stereotype.Component class * { *; }
-keep @org.springframework.stereotype.Service class * { *; }
-keep @org.springframework.stereotype.Repository class * { *; }
-keep @org.springframework.stereotype.Controller class * { *; }
-keep @org.springframework.web.bind.annotation.RestController class * { *; }
-keep @org.springframework.boot.autoconfigure.SpringBootApplication class * { *; }
-keep @org.springframework.context.annotation.Configuration class * { *; }

# 保留Spring Web相关注解的方法
-keepclassmembers class * {
    @org.springframework.web.bind.annotation.* <methods>;
    @org.springframework.boot.context.properties.ConfigurationProperties *;
    @org.springframework.beans.factory.annotation.Autowired *;
    @org.springframework.beans.factory.annotation.Value *;
    @org.springframework.context.annotation.Bean *;
}

# 保留Spring Boot自动配置相关
-keep class org.springframework.boot.** { *; }
-keep class org.springframework.context.annotation.** { *; }
-keep class org.springframework.boot.autoconfigure.** { *; }

# 保留Spring Security相关
-keep class org.springframework.security.** { *; }
-keep @org.springframework.security.access.prepost.PreAuthorize class * { *; }

#########################################
# 数据库相关配置
#########################################

# MyBatis相关
-keep class org.apache.ibatis.** { *; }
-keep class com.baomidou.mybatisplus.** { *; }
-keep interface com.quickdatax.**.mapper.** { *; }
-keep interface com.quickdatax.**.dal.mysql.** { *; }

# 保留所有DO（数据对象）类
-keep class com.quickdatax.**.dal.dataobject.** { *; }

# 保留Mapper接口和注解
-keepclassmembers interface * {
    @org.apache.ibatis.annotations.* *;
}

# 数据源相关
-keep class com.alibaba.druid.** { *; }
-keep class com.baomidou.dynamic.datasource.** { *; }

#########################################
# JSON序列化相关配置
#########################################

# Jackson相关
-keepclassmembers class * {
    @com.fasterxml.jackson.annotation.* *;
}
-keep @com.fasterxml.jackson.annotation.* class * { *; }
-keep class com.fasterxml.jackson.** { *; }

# FastJSON相关
-keep class com.alibaba.fastjson.** { *; }

# 保留所有VO、DTO类（用于JSON序列化）
-keep class com.quickdatax.**.controller.**.vo.** { *; }
-keep class com.quickdatax.**.api.**.dto.** { *; }

#########################################
# Lombok 支持
#########################################

# 保留Lombok生成的方法
-keep class * {
    @lombok.Generated <methods>;
    @lombok.Generated <fields>;
}
-keepclassmembers class * {
    @lombok.Generated <methods>;
    @lombok.Generated <fields>;
}

# 保留Lombok注解标注的类
-keep @lombok.Generated class * { *; }
-keep @lombok.AllArgsConstructor class * { *; }
-keep @lombok.NoArgsConstructor class * { *; }
-keep @lombok.RequiredArgsConstructor class * { *; }
-keep @lombok.Getter class * { *; }
-keep @lombok.Setter class * { *; }
-keep @lombok.Data class * { *; }
-keep @lombok.Builder class * { *; }
-keep @lombok.ToString class * { *; }
-keep @lombok.EqualsAndHashCode class * { *; }

#########################################
# 第三方库配置
#########################################

# Redis相关
-keep class org.springframework.data.redis.** { *; }
-keep class org.redisson.** { *; }
-keep class redis.clients.jedis.** { *; }

# 消息队列相关
-keep class org.springframework.amqp.** { *; }
-keep class org.apache.rocketmq.** { *; }
-keep class org.springframework.kafka.** { *; }

# 工具库
-keep class cn.hutool.** { *; }
-keep class com.google.guava.** { *; }
-keep class org.apache.commons.** { *; }

# 验证相关
-keep class javax.validation.** { *; }
-keep class jakarta.validation.** { *; }
-keep class org.hibernate.validator.** { *; }

# 定时任务
-keep class org.quartz.** { *; }
-keep class com.xxl.job.** { *; }

# 监控相关
-keep class org.springframework.boot.actuator.** { *; }
-keep class io.micrometer.** { *; }

#########################################
# 项目特定配置
#########################################

# 保留所有枚举类
-keepclassmembers enum * { *; }

# 保留所有异常类
-keep class * extends java.lang.Exception { *; }
-keep class * extends java.lang.RuntimeException { *; }

# 保留所有实现Serializable的类
-keep class * implements java.io.Serializable { *; }
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# 保留所有API接口类
-keep class com.quickdatax.**.api.** { *; }

# 保留所有配置类
-keep class com.quickdatax.**.config.** { *; }
-keep class com.quickdatax.**.configuration.** { *; }

# 保留所有常量类
-keep class com.quickdatax.**.constant.** { *; }
-keep class com.quickdatax.**.constants.** { *; }

# 保留所有工具类
-keep class com.quickdatax.**.util.** { *; }
-keep class com.quickdatax.**.utils.** { *; }

#########################################
# 反射相关配置
#########################################

# 保留反射相关的方法
-keepclassmembers class * {
    public <init>(...);
    public void set*(...);
    public *** get*();
    public *** is*();
}

# 保留所有native方法
-keepclasseswithmembernames class * {
    native <methods>;
}

#########################################
# 日志相关配置
#########################################

-keep class org.slf4j.** { *; }
-keep class ch.qos.logback.** { *; }
-keep class org.apache.logging.log4j.** { *; }

#########################################
# 安全和性能配置
#########################################

# 生成混淆映射文件
-printmapping build/proguard/mapping.txt
-printseeds build/proguard/seeds.txt
-printusage build/proguard/usage.txt

# 重命名包名（可选，增强混淆效果）
# -repackageclasses com.quickdatax.obfuscated

# 使用自定义字典进行混淆（可选）
# -classobfuscationdictionary dictionary.txt
# -obfuscationdictionary dictionary.txt

#########################################
# 特殊处理
#########################################

# 保留内部类
-keepattributes InnerClasses
-keep class com.quickdatax.**$* { *; }

# 保留泛型参数
-keepattributes Signature

# 保留注解参数
-keepattributes *Annotation*,AnnotationDefault

# 保留行号信息（用于异常堆栈跟踪）
-keepattributes SourceFile,LineNumberTable
