<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.quickdatax</groupId>
        <artifactId>quickdatax</artifactId>
        <version>${revision}</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>quickdatax-open-api</artifactId>
    <name>quickdatax-open-api</name>
    <description>对外开放接口</description>

    <dependencies>
        <dependency>
            <groupId>com.quickdatax</groupId>
            <artifactId>quickdatax-module-bms-biz</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.quickdatax</groupId>
            <artifactId>quickdatax-module-infra-biz</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.quickdatax</groupId>
            <artifactId>quickdatax-module-system-biz</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.quickdatax</groupId>
            <artifactId>quickdatax-module-pay-biz</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- spring boot 配置所需依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- 服务保障相关 -->
        <dependency>
            <groupId>com.quickdatax</groupId>
            <artifactId>quickdatax-spring-boot-starter-protection</artifactId>
        </dependency>

    </dependencies>

    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- 1. 混淆插件 -->
            <plugin>
                <groupId>com.github.wvengen</groupId>
                <artifactId>proguard-maven-plugin</artifactId>
                <version>2.7.0</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals><goal>proguard</goal></goals>
                    </execution>
                </executions>
                <configuration>
                    <obfuscate>true</obfuscate>
                    <injar>${project.build.finalName}.jar</injar>
                    <outjar>${project.build.finalName}-obfuscated.jar</outjar>
                    <outputDirectory>${project.build.directory}</outputDirectory>
                    <addManifest>true</addManifest>
                    <addMavenDescriptor>false</addMavenDescriptor>
                    <includeDependency>true</includeDependency>
                    <includeDependencyInjar>false</includeDependencyInjar>
                    <assembly>
                        <inclusions>
                            <inclusion>
                                <groupId>com.quickdatax</groupId>
                                <artifactId>*</artifactId>
                            </inclusion>
                        </inclusions>
                    </assembly>
                    <archive>
                        <manifest>
                            <mainClass>com.quickdatax.open.QuickdataxOpenApiApplication</mainClass>
                            <addClasspath>true</addClasspath>
                        </manifest>
                        <manifestEntries>
                            <Built-By>${user.name}</Built-By>
                        </manifestEntries>
                        <addMavenDescriptor>false</addMavenDescriptor>
                    </archive>
                    <generateTemporaryConfigurationFile>true</generateTemporaryConfigurationFile>

                    <proguardInclude>${project.basedir}/proguard-rules-open-api.pro</proguardInclude>

                    <libs>
                        <lib>${java.home}/lib/rt.jar</lib>
                        <lib>${java.home}/lib/jsse.jar</lib>
                        <lib>${java.home}/lib/jce.jar</lib>
                    </libs>
                </configuration>
            </plugin>
            <!-- 2. Spring Boot 可执行打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <mainClass>
                        com.quickdatax.open.QuickdataxOpenApiApplication
                    </mainClass>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
