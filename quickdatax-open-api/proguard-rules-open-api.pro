#########################################
# QuickDataX Open API ProGuard 混淆配置
# 专门针对 quickdatax-open-api 模块
#########################################

#########################################
# 基础配置
#########################################

-dontwarn
-ignorewarnings
-dontoptimize
-dontpreverify
-dontshrink

# 保留所有属性信息
-keepattributes *Annotation*,Signature,InnerClasses,EnclosingMethod,Exceptions,LineNumberTable,SourceFile

#########################################
# 保留入口类
#########################################

-keep public class com.quickdatax.open.QuickdataxOpenApiApplication {
    public static void main(java.lang.String[]);
}

#########################################
# Spring 框架完整保留
#########################################

# 保留所有Spring相关类
-keep class org.springframework.** { *; }
-keep @org.springframework.** class * { *; }

# 保留Spring注解标注的类
-keep @org.springframework.stereotype.Component class * { *; }
-keep @org.springframework.stereotype.Service class * { *; }
-keep @org.springframework.stereotype.Repository class * { *; }
-keep @org.springframework.stereotype.Controller class * { *; }
-keep @org.springframework.web.bind.annotation.RestController class * { *; }
-keep @org.springframework.boot.autoconfigure.SpringBootApplication class * { *; }
-keep @org.springframework.context.annotation.Configuration class * { *; }

# 保留所有Spring注解的方法和字段
-keepclassmembers class * {
    @org.springframework.** *;
}

#########################################
# 项目代码选择性保留
#########################################

# 保留API接口类（对外暴露的）
-keep class com.quickdatax.**.api.** { *; }
-keep class com.quickdatax.**.controller.** { *; }

# 保留配置类
-keep class com.quickdatax.**.config.** { *; }
-keep class com.quickdatax.**.configuration.** { *; }

# 保留常量类
-keep class com.quickdatax.**.constant.** { *; }
-keep class com.quickdatax.**.constants.** { *; }

# 保留VO、DTO类（用于JSON序列化）
-keep class com.quickdatax.**.vo.** { *; }
-keep class com.quickdatax.**.dto.** { *; }

# 保留数据对象
-keep class com.quickdatax.**.dal.dataobject.** { *; }

# 保留Mapper接口
-keep interface com.quickdatax.**.mapper.** { *; }

# 其他业务逻辑类可以混淆（注释掉下面这行来启用混淆）
# -keep class com.quickdatax.** { *; }

# 保留所有枚举
-keepclassmembers enum * { *; }

# 保留所有异常类
-keep class * extends java.lang.Exception { *; }
-keep class * extends java.lang.RuntimeException { *; }

# 保留所有实现Serializable的类
-keep class * implements java.io.Serializable { *; }
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

#########################################
# 数据库相关完整保留
#########################################

# MyBatis完整保留
-keep class org.apache.ibatis.** { *; }
-keep class com.baomidou.mybatisplus.** { *; }
-keep class com.quickdatax.**.mapper.** { *; }
-keep class com.quickdatax.**.dal.** { *; }

# 数据源相关
-keep class com.alibaba.druid.** { *; }
-keep class com.baomidou.dynamic.datasource.** { *; }

#########################################
# JSON序列化完整保留
#########################################

# Jackson完整保留
-keep class com.fasterxml.jackson.** { *; }
-keepclassmembers class * {
    @com.fasterxml.jackson.annotation.* *;
}

# FastJSON完整保留
-keep class com.alibaba.fastjson.** { *; }

# 所有VO、DTO、Entity类
-keep class com.quickdatax.**.controller.** { *; }
-keep class com.quickdatax.**.api.** { *; }
-keep class com.quickdatax.**.entity.** { *; }
-keep class com.quickdatax.**.model.** { *; }

#########################################
# Lombok完整支持
#########################################

# 保留所有Lombok相关
-keep class lombok.** { *; }
-keep @lombok.** class * { *; }
-keepclassmembers class * {
    @lombok.** *;
}

#########################################
# 第三方库完整保留
#########################################

# Redis相关
-keep class org.springframework.data.redis.** { *; }
-keep class org.redisson.** { *; }
-keep class redis.clients.jedis.** { *; }

# 工具库
-keep class cn.hutool.** { *; }
-keep class com.google.guava.** { *; }
-keep class org.apache.commons.** { *; }

# 验证相关
-keep class javax.validation.** { *; }
-keep class jakarta.validation.** { *; }
-keep class org.hibernate.validator.** { *; }

# 定时任务
-keep class org.quartz.** { *; }
-keep class com.xxl.job.** { *; }

# 监控相关
-keep class org.springframework.boot.actuator.** { *; }
-keep class io.micrometer.** { *; }

# 日志相关
-keep class org.slf4j.** { *; }
-keep class ch.qos.logback.** { *; }
-keep class org.apache.logging.log4j.** { *; }

#########################################
# 反射支持
#########################################

# 保留所有反射相关的方法
-keepclassmembers class * {
    public <init>(...);
    public void set*(...);
    public *** get*();
    public *** is*();
    public <fields>;
    public <methods>;
}

# 保留所有native方法
-keepclasseswithmembernames class * {
    native <methods>;
}

#########################################
# 忽略可选依赖的警告
#########################################

# 消息队列相关（可选依赖）
-dontwarn org.springframework.amqp.**
-dontwarn org.apache.rocketmq.**
-dontwarn org.springframework.kafka.**
-dontwarn org.apache.kafka.**

# Lock4j相关（可选依赖）
-dontwarn com.baomidou.lock.**

# 其他可选依赖
-dontwarn com.xxl.job.**
-dontwarn org.quartz.**

#########################################
# 保留内部类和泛型
#########################################

# 保留所有内部类
-keep class com.quickdatax.**$* { *; }

# 保留所有泛型信息
-keepattributes Signature

# 保留所有注解信息
-keepattributes *Annotation*,AnnotationDefault

# 保留源文件和行号信息（便于调试）
-keepattributes SourceFile,LineNumberTable

# 保留异常信息
-keepattributes Exceptions

#########################################
# 生成详细的映射文件
#########################################

-printmapping build/proguard/mapping-open-api.txt
-printseeds build/proguard/seeds-open-api.txt
-printusage build/proguard/usage-open-api.txt

# 输出详细信息
-verbose

#########################################
# 特殊处理
#########################################

# 保留所有注解类本身
-keep @interface *

# 保留所有配置类
-keep class com.quickdatax.**.config.** { *; }
-keep class com.quickdatax.**.configuration.** { *; }

# 保留所有常量类
-keep class com.quickdatax.**.constant.** { *; }
-keep class com.quickdatax.**.constants.** { *; }

# 保留所有工具类
-keep class com.quickdatax.**.util.** { *; }
-keep class com.quickdatax.**.utils.** { *; }
