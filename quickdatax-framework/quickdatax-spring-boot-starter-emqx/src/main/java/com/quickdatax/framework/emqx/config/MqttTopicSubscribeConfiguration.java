package com.quickdatax.framework.emqx.config;

import com.quickdatax.framework.emqx.core.AbstractMqttSubscriptionProvider;
import com.quickdatax.framework.emqx.core.MqttSubscriptionProvider;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.mqttv5.client.*;
import org.eclipse.paho.mqttv5.common.MqttException;
import org.eclipse.paho.mqttv5.common.MqttMessage;
import org.eclipse.paho.mqttv5.common.MqttSubscription;
import org.eclipse.paho.mqttv5.common.packet.MqttProperties;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 订阅主题
 * </p>
 * 如果想新增订阅主题，只需要扩展{@link AbstractMqttSubscriptionProvider}
 * <AUTHOR>
 * @date 2024/11/21
 */
@Slf4j
@Configuration
@ConditionalOnProperty(prefix = "quickdatax.emqx", value = "subscription", matchIfMissing = true)
public class MqttTopicSubscribeConfiguration implements SmartInitializingSingleton {
    @Resource
    private MqttClient mqttClient;

    @Resource
    private List<MqttSubscriptionProvider> mqttSubscriptionProviders;

    @Override
    public void afterSingletonsInstantiated() {
        initCallback();
        initSub();
    }

    private void initSub() {
        if (mqttSubscriptionProviders == null || mqttSubscriptionProviders.isEmpty()) {
            return;
        }
        MqttSubscription[] mqttSubscriptions = mqttSubscriptionProviders.stream().map(MqttSubscriptionProvider::getMqttSubscription).toArray(MqttSubscription[]::new);
        IMqttMessageListener[] mqttMessageListeners = mqttSubscriptionProviders.stream().map(MqttSubscriptionProvider::getMessageListener).toArray(IMqttMessageListener[]::new);
        try {
            log.info("subscribe topic: {}", Arrays.toString(mqttSubscriptions));
            mqttClient.subscribe(mqttSubscriptions, mqttMessageListeners);
        } catch (MqttException e) {
            log.error("subscribe topic error", e);
            throw new RuntimeException(e);
        }
    }

    private void initCallback() {
        MqttCallback mqttCallback = new MqttCallback() {
            @Override
            public void disconnected(MqttDisconnectResponse disconnectResponse) {
                log.info("emqx client disconnected");
            }

            @Override
            public void mqttErrorOccurred(MqttException exception) {
                log.error("emqx mqttErrorOccurred", exception);
            }

            @Override
            public void messageArrived(String topic, MqttMessage message) throws Exception {
                log.info("emqx messageArrived");
            }

            @Override
            public void deliveryComplete(IMqttToken token) {
                log.info("emqx deliveryComplete");
            }

            @Override
            public void connectComplete(boolean reconnect, String serverURI) {
                if (mqttClient.isConnected()) {
                    log.info("reconnect success");
                    initSub();
                } else {
                    log.error("reconnect failed");
                }
            }

            @Override
            public void authPacketArrived(int reasonCode, MqttProperties properties) {
            }

        };
        mqttClient.setCallback(mqttCallback);
    }

}
