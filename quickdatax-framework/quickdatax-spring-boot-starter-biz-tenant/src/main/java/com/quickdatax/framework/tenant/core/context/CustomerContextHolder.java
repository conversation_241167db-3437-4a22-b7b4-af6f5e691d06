package com.quickdatax.framework.tenant.core.context;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.quickdatax.framework.common.enums.DocumentEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @date 2024/07/02
 */
public class CustomerContextHolder {

    /**
     * 当前客户编号
     */
    private static final ThreadLocal<Integer> CUSTOMER_ID = new TransmittableThreadLocal<>();

    /**
     * 是否忽略租户
     */
    private static final ThreadLocal<Boolean> IGNORE = new TransmittableThreadLocal<>();
    private static final Logger log = LoggerFactory.getLogger(CustomerContextHolder.class);

    /**
     * 获得客户编号。
     *
     * @return 客户编号
     */
    public static Integer getCustomerId() {
        return CUSTOMER_ID.get();
    }

    /**
     * 获得客户编号。如果不存在，则抛出 NullPointerException 异常
     *
     * @return 客户编号
     */
    public static Integer getRequiredCustomerId() {
        Integer customerId = getCustomerId();
        if (customerId == null) {
            throw new NullPointerException(
                "CustomerContextHolder 不存在客户编号！可参考文档：" + DocumentEnum.TENANT.getUrl());
        }
        return customerId;
    }

    public static void setCustomerId(Integer customerId) {
        log.debug("CustomerContextHolder change customerId to :{}", customerId);
        CUSTOMER_ID.set(customerId);
    }

    public static void setIgnore(Boolean ignore) {
        IGNORE.set(ignore);
    }

    /**
     * 当前是否忽略客户
     *
     * @return 是否忽略
     */
    public static boolean isIgnore() {
        return Boolean.TRUE.equals(IGNORE.get());
    }

    public static void clear() {
        log.debug("CustomerContextHolder remove customerId :{}", CUSTOMER_ID.get());
        CUSTOMER_ID.remove();
        IGNORE.remove();
    }
}
