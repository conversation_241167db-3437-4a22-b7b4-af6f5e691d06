<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.quickdatax</groupId>
        <artifactId>quickdatax-module-report</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>quickdatax-module-report-biz</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        report 模块，主要实现数据可视化报表等功能：
        1. 基于「积木报表」实现，打印设计、报表设计、图形设计、大屏设计等。
    </description>
    <dependencies>
        <dependency>
            <groupId>com.quickdatax</groupId>
            <artifactId>quickdatax-module-report-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.quickdatax</groupId>
            <artifactId>quickdatax-module-system-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>com.quickdatax</groupId>
            <artifactId>quickdatax-spring-boot-starter-biz-tenant</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>com.quickdatax</groupId>
            <artifactId>quickdatax-spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.quickdatax</groupId>
            <artifactId>quickdatax-spring-boot-starter-security</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.quickdatax</groupId>
            <artifactId>quickdatax-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>com.quickdatax</groupId>
            <artifactId>quickdatax-spring-boot-starter-test</artifactId>
        </dependency>

        <!-- 积木报表-->
        <dependency>
            <groupId>org.jeecgframework.jimureport</groupId>
            <artifactId>jimureport-spring-boot-starter</artifactId>
        </dependency>
        <!-- 单独依赖升级版本，解决低版本validator失败问题 -->
        <dependency>
            <groupId>xerces</groupId>
            <artifactId>xercesImpl</artifactId>
        </dependency>

        <dependency>
            <groupId>com.quickdatax</groupId>
            <artifactId>quickdatax-spring-boot-starter-excel</artifactId>
        </dependency>

    </dependencies>
</project>
