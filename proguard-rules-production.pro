#########################################
# QuickDataX 生产环境 ProGuard 混淆配置
# 更激进的混淆策略，适用于生产环境部署
#########################################

#########################################
# 基础配置
#########################################

-dontwarn
-ignorewarnings
-dontoptimize         # 禁用优化，避免反射问题
-dontpreverify
-keepattributes Signature,InnerClasses,EnclosingMethod,Exceptions
# 生产环境可以移除调试信息
# -keepattributes SourceFile,LineNumberTable

#########################################
# 保留入口类
#########################################

-keep class com.quickdatax.server.QuickdataxServerApplication {
    public static void main(java.lang.String[]);
}

-keep class com.quickdatax.client.QuickdataxClientApplication {
    public static void main(java.lang.String[]);
}

#########################################
# Spring 框架核心配置
#########################################

# 保留Spring核心注解
-keep @org.springframework.stereotype.Component class * { *; }
-keep @org.springframework.stereotype.Service class * { *; }
-keep @org.springframework.stereotype.Repository class * { *; }
-keep @org.springframework.web.bind.annotation.RestController class * { *; }
-keep @org.springframework.boot.autoconfigure.SpringBootApplication class * { *; }
-keep @org.springframework.context.annotation.Configuration class * { *; }

# 保留Spring Web注解方法
-keepclassmembers class * {
    @org.springframework.web.bind.annotation.* <methods>;
    @org.springframework.beans.factory.annotation.Autowired *;
    @org.springframework.beans.factory.annotation.Value *;
    @org.springframework.context.annotation.Bean *;
}

# Spring Boot核心类
-keep class org.springframework.boot.SpringApplication { *; }
-keep class org.springframework.boot.autoconfigure.** { *; }

#########################################
# 数据库相关（精简版）
#########################################

# MyBatis核心
-keep class org.apache.ibatis.** { *; }
-keep class com.baomidou.mybatisplus.core.** { *; }
-keep interface com.quickdatax.**.mapper.** { *; }

# 数据对象
-keep class com.quickdatax.**.dal.dataobject.** { *; }

# Mapper注解
-keepclassmembers interface * {
    @org.apache.ibatis.annotations.* *;
}

#########################################
# JSON序列化（精简版）
#########################################

# Jackson核心
-keep class com.fasterxml.jackson.databind.** { *; }
-keepclassmembers class * {
    @com.fasterxml.jackson.annotation.* *;
}

# VO/DTO类
-keep class com.quickdatax.**.controller.**.vo.** { *; }
-keep class com.quickdatax.**.api.**.dto.** { *; }

#########################################
# Lombok（精简版）
#########################################

-keepclassmembers class * {
    @lombok.Generated <methods>;
    @lombok.Generated <fields>;
}

#########################################
# 项目核心配置
#########################################

# 枚举类
-keepclassmembers enum * { *; }

# 异常类
-keep class * extends java.lang.Exception { *; }

# API接口
-keep class com.quickdatax.**.api.** { *; }

# 配置类
-keep class com.quickdatax.**.config.** { *; }

# 常量类
-keep class com.quickdatax.**.constant.** { *; }

#########################################
# 反射支持
#########################################

-keepclassmembers class * {
    public <init>(...);
    public void set*(...);
    public *** get*();
}

#########################################
# 生产环境优化
#########################################

# 启用代码压缩（生产环境可选）
# -dontshrink

# 生成映射文件
-printmapping build/proguard/mapping-prod.txt

# 重命名包名（增强混淆）
-repackageclasses com.quickdatax.obf

# 移除未使用的类和方法
-printusage build/proguard/usage-prod.txt

#########################################
# 安全增强
#########################################

# 混淆类名和方法名
-useuniqueclassmembernames
-adaptclassstrings

# 保留必要的属性
-keepattributes *Annotation*

# 移除调试信息（生产环境）
-assumenosideeffects class android.util.Log {
    public static *** d(...);
    public static *** v(...);
    public static *** i(...);
}

# 移除System.out.println（可选）
-assumenosideeffects class java.io.PrintStream {
    public void println(...);
    public void print(...);
}

#########################################
# 第三方库（最小化保留）
#########################################

# Redis核心
-keep class org.springframework.data.redis.core.** { *; }
-keep class org.redisson.api.** { *; }

# 工具库核心
-keep class cn.hutool.core.** { *; }

# 验证框架
-keep class javax.validation.** { *; }
-keep class jakarta.validation.** { *; }

# 日志框架
-keep class org.slf4j.Logger { *; }
-keep class org.slf4j.LoggerFactory { *; }
