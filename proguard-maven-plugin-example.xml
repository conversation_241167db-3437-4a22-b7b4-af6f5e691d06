<!-- 
QuickDataX ProGuard Maven 插件配置示例
将此配置添加到需要混淆的模块的 pom.xml 文件中
-->

<plugin>
    <groupId>com.github.wvengen</groupId>
    <artifactId>proguard-maven-plugin</artifactId>
    <version>2.7.0</version>
    <executions>
        <execution>
            <phase>package</phase>
            <goals>
                <goal>proguard</goal>
            </goals>
        </execution>
    </executions>
    <configuration>
        <!-- 输入JAR文件 -->
        <injar>${project.build.finalName}.jar</injar>
        <!-- 输出JAR文件 -->
        <outjar>${project.build.finalName}-obfuscated.jar</outjar>
        
        <!-- 输出目录 -->
        <outputDirectory>${project.build.directory}</outputDirectory>
        
        <!-- 根据环境选择不同的配置文件 -->
        <proguardInclude>${basedir}/proguard-rules-${spring.profiles.active}.pro</proguardInclude>
        
        <!-- 备用配置：如果没有指定环境，使用综合配置 -->
        <!-- <proguardInclude>${basedir}/proguard-rules-comprehensive.pro</proguardInclude> -->
        
        <!-- 库JAR文件路径 -->
        <libs>
            <!-- Java运行时库 -->
            <lib>${java.home}/lib/rt.jar</lib>
            <lib>${java.home}/lib/jsse.jar</lib>
            <lib>${java.home}/lib/jce.jar</lib>
            
            <!-- 如果使用JDK 9+，使用以下配置 -->
            <!-- <lib>${java.home}/jmods</lib> -->
        </libs>
        
        <!-- 附加选项 -->
        <options>
            <!-- 忽略警告 -->
            <option>-ignorewarnings</option>
            <!-- 不优化 -->
            <option>-dontoptimize</option>
            <!-- 不预验证 -->
            <option>-dontpreverify</option>
            <!-- 保留属性 -->
            <option>-keepattributes Signature,InnerClasses,EnclosingMethod,Exceptions,*Annotation*</option>
            
            <!-- 生产环境可以启用以下选项 -->
            <!-- <option>-repackageclasses com.quickdatax.obfuscated</option> -->
            <!-- <option>-allowaccessmodification</option> -->
            
            <!-- 输出映射文件 -->
            <option>-printmapping ${project.build.directory}/proguard/mapping.txt</option>
            <option>-printseeds ${project.build.directory}/proguard/seeds.txt</option>
            <option>-printusage ${project.build.directory}/proguard/usage.txt</option>
        </options>
        
        <!-- 最大内存 -->
        <maxMemory>2048m</maxMemory>
        
        <!-- 包含依赖 -->
        <includeDependency>true</includeDependency>
        
        <!-- 排除某些依赖（可选） -->
        <exclusions>
            <exclusion>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-devtools</artifactId>
            </exclusion>
        </exclusions>
    </configuration>
</plugin>

<!-- 
使用Profile来区分不同环境的混淆配置
-->
<profiles>
    <!-- 开发环境 -->
    <profile>
        <id>dev</id>
        <activation>
            <activeByDefault>true</activeByDefault>
        </activation>
        <properties>
            <spring.profiles.active>development</spring.profiles.active>
        </properties>
        <build>
            <plugins>
                <plugin>
                    <groupId>com.github.wvengen</groupId>
                    <artifactId>proguard-maven-plugin</artifactId>
                    <configuration>
                        <proguardInclude>${basedir}/proguard-rules-development.pro</proguardInclude>
                        <!-- 开发环境可以跳过混淆 -->
                        <skip>true</skip>
                    </configuration>
                </plugin>
            </plugins>
        </build>
    </profile>
    
    <!-- 测试环境 -->
    <profile>
        <id>test</id>
        <properties>
            <spring.profiles.active>test</spring.profiles.active>
        </properties>
        <build>
            <plugins>
                <plugin>
                    <groupId>com.github.wvengen</groupId>
                    <artifactId>proguard-maven-plugin</artifactId>
                    <configuration>
                        <proguardInclude>${basedir}/proguard-rules-comprehensive.pro</proguardInclude>
                    </configuration>
                </plugin>
            </plugins>
        </build>
    </profile>
    
    <!-- 生产环境 -->
    <profile>
        <id>prod</id>
        <properties>
            <spring.profiles.active>production</spring.profiles.active>
        </properties>
        <build>
            <plugins>
                <plugin>
                    <groupId>com.github.wvengen</groupId>
                    <artifactId>proguard-maven-plugin</artifactId>
                    <configuration>
                        <proguardInclude>${basedir}/proguard-rules-production.pro</proguardInclude>
                        <!-- 生产环境启用更激进的混淆 -->
                        <options>
                            <option>-ignorewarnings</option>
                            <option>-dontoptimize</option>
                            <option>-dontpreverify</option>
                            <option>-repackageclasses com.quickdatax.obf</option>
                            <option>-allowaccessmodification</option>
                            <option>-useuniqueclassmembernames</option>
                            <option>-adaptclassstrings</option>
                            <option>-printmapping ${project.build.directory}/proguard/mapping-prod.txt</option>
                        </options>
                    </configuration>
                </plugin>
            </plugins>
        </build>
    </profile>
</profiles>

<!-- 
使用示例：

1. 开发环境（跳过混淆）：
   mvn clean package -Pdev

2. 测试环境（标准混淆）：
   mvn clean package -Ptest

3. 生产环境（激进混淆）：
   mvn clean package -Pprod

4. 使用默认配置：
   mvn clean package

注意事项：
1. 确保将配置文件放在正确的位置
2. 根据实际需要调整配置
3. 生产环境部署前务必测试混淆后的JAR文件
4. 保存好mapping.txt文件，用于问题排查
-->
