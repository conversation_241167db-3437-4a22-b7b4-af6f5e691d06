@echo off
echo ========================================
echo QuickDataX ProGuard 问题修复脚本
echo ========================================

echo.
echo 1. 停止所有Java进程...
taskkill /f /im java.exe 2>nul
timeout /t 2 >nul

echo.
echo 2. 清理target目录...
if exist "quickdatax-client\target" (
    echo 删除 quickdatax-client\target 目录...
    rmdir /s /q "quickdatax-client\target" 2>nul
)

echo.
echo 3. 备份原配置文件...
if exist "quickdatax-client\src\main\resources\proguard\proguard-rules1.pro" (
    copy "quickdatax-client\src\main\resources\proguard\proguard-rules1.pro" "quickdatax-client\src\main\resources\proguard\proguard-rules1.pro.backup" >nul
    echo 原配置文件已备份为 proguard-rules1.pro.backup
)

echo.
echo 4. 检查修复配置文件是否存在...
if exist "quickdatax-client\src\main\resources\proguard\proguard-rules-fixed.pro" (
    echo 修复配置文件已存在
) else (
    echo 错误：修复配置文件不存在，请先运行AI助手生成配置文件
    pause
    exit /b 1
)

echo.
echo 5. 重新构建项目...
echo 正在构建 quickdatax-client 模块...
cd quickdatax-client
call mvn clean package -DskipTests -q

if %ERRORLEVEL% EQU 0 (
    echo 构建成功！
) else (
    echo 构建失败，请检查错误信息
    cd ..
    pause
    exit /b 1
)

cd ..

echo.
echo 6. 检查生成的JAR文件...
if exist "quickdatax-client\target\quickdatax-client-2.1.0-jdk8-snapshot-obfuscated.jar" (
    echo 混淆后的JAR文件生成成功！
    echo 文件位置: quickdatax-client\target\quickdatax-client-2.1.0-jdk8-snapshot-obfuscated.jar
) else (
    echo 警告：混淆后的JAR文件未找到
)

echo.
echo 7. 测试运行混淆后的JAR文件...
echo 正在启动应用程序进行测试...
cd quickdatax-client\target
timeout /t 3 >nul

echo 尝试运行混淆后的JAR文件（10秒后自动停止）...
start /b java -jar quickdatax-client-2.1.0-jdk8-snapshot-obfuscated.jar
timeout /t 10 >nul
taskkill /f /im java.exe 2>nul

cd ..\..

echo.
echo ========================================
echo 修复完成！
echo ========================================
echo.
echo 下一步操作：
echo 1. 手动测试混淆后的JAR文件：
echo    cd quickdatax-client\target
echo    java -jar quickdatax-client-2.1.0-jdk8-snapshot-obfuscated.jar
echo.
echo 2. 如果仍有问题，请查看 ProGuard-Fix-Solution.md 文档
echo.
echo 3. 检查生成的映射文件：
echo    quickdatax-client\target\proguard\mapping-fixed.txt
echo.
pause
