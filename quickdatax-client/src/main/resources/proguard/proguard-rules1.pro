#########################################
# QuickDataX Spring Boot Fat JAR 混淆配置
# 修复版本 - 解决 VerifyError 问题
#########################################

#########################################
# 基础配置
#########################################

-dontwarn
-ignorewarnings
-dontoptimize         # 禁用优化
-dontpreverify
-dontshrink           # 禁用代码压缩，保留所有代码便于调试
-keepattributes *     # 保留所有属性信息，便于调试

#########################################
# Spring Boot Fat JAR 特殊配置
#########################################

# 保留Spring Boot Loader相关类（关键！）
-keep class org.springframework.boot.loader.** { *; }
-keep class org.springframework.boot.loader.archive.** { *; }
-keep class org.springframework.boot.loader.jar.** { *; }
-keep class org.springframework.boot.loader.util.** { *; }

# 保留MANIFEST.MF相关信息
-keepattributes Manifest
-keep class * {
    public static void main(java.lang.String[]);
}

# 保留JAR文件结构
-keepdirectories
-keepattributes *Annotation*,Signature,InnerClasses,EnclosingMethod

#########################################
# 保留入口类
#########################################

-keep class com.quickdatax.server.QuickdataxServerApplication {
    public static void main(java.lang.String[]);
}

-keep class com.quickdatax.client.QuickdataxClientApplication {
    public static void main(java.lang.String[]);
}

#########################################
# Spring 框架完整保留
#########################################

# 保留所有Spring相关类和方法
-keep class org.springframework.** { *; }
-keep @org.springframework.** class * { *; }

# 保留Spring Boot自动配置
-keep class org.springframework.boot.autoconfigure.** { *; }
-keep class org.springframework.boot.context.** { *; }
-keep class org.springframework.boot.env.** { *; }

# 保留所有注解
-keep @interface *

# 保留Spring注解标注的类
-keep @org.springframework.stereotype.Component class * { *; }
-keep @org.springframework.stereotype.Service class * { *; }
-keep @org.springframework.stereotype.Repository class * { *; }
-keep @org.springframework.stereotype.Controller class * { *; }
-keep @org.springframework.web.bind.annotation.RestController class * { *; }
-keep @org.springframework.boot.autoconfigure.SpringBootApplication class * { *; }
-keep @org.springframework.context.annotation.Configuration class * { *; }

# 保留所有Spring注解的方法和字段
-keepclassmembers class * {
    @org.springframework.** *;
}

# 保留Spring Boot配置属性类
-keep @org.springframework.boot.context.properties.ConfigurationProperties class * { *; }
-keepclassmembers @org.springframework.boot.context.properties.ConfigurationProperties class * {
    <fields>;
    <methods>;
}

#########################################
# 数据库相关完整保留
#########################################

# MyBatis完整保留
-keep class org.apache.ibatis.** { *; }
-keep class com.baomidou.mybatisplus.** { *; }
-keep class com.quickdatax.**.mapper.** { *; }
-keep class com.quickdatax.**.dal.** { *; }

# 数据源相关
-keep class com.alibaba.druid.** { *; }
-keep class com.baomidou.dynamic.datasource.** { *; }

#########################################
# JSON序列化完整保留
#########################################

# Jackson完整保留
-keep class com.fasterxml.jackson.** { *; }
-keepclassmembers class * {
    @com.fasterxml.jackson.annotation.* *;
}

# FastJSON完整保留
-keep class com.alibaba.fastjson.** { *; }

# 所有VO、DTO、Entity类
-keep class com.quickdatax.**.controller.** { *; }
-keep class com.quickdatax.**.api.** { *; }
-keep class com.quickdatax.**.entity.** { *; }
-keep class com.quickdatax.**.model.** { *; }

#########################################
# Lombok完整支持
#########################################

# 保留所有Lombok相关
-keep class lombok.** { *; }
-keep @lombok.** class * { *; }
-keepclassmembers class * {
    @lombok.** *;
}

#########################################
# 项目代码完整保留
#########################################

# 保留所有项目包下的类
-keep class com.quickdatax.** { *; }

# 保留所有枚举
-keepclassmembers enum * { *; }

# 保留所有异常类
-keep class * extends java.lang.Exception { *; }
-keep class * extends java.lang.RuntimeException { *; }

# 保留所有实现Serializable的类
-keep class * implements java.io.Serializable { *; }
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

#########################################
# 第三方库完整保留
#########################################

# Redis相关
-keep class org.springframework.data.redis.** { *; }
-keep class org.redisson.** { *; }
-keep class redis.clients.jedis.** { *; }

# 消息队列相关
-keep class org.springframework.amqp.** { *; }
-keep class org.apache.rocketmq.** { *; }
-keep class org.springframework.kafka.** { *; }

# 工具库
-keep class cn.hutool.** { *; }
-keep class com.google.guava.** { *; }
-keep class org.apache.commons.** { *; }

# 验证相关
-keep class javax.validation.** { *; }
-keep class jakarta.validation.** { *; }
-keep class org.hibernate.validator.** { *; }

# 定时任务
-keep class org.quartz.** { *; }
-keep class com.xxl.job.** { *; }

# 监控相关
-keep class org.springframework.boot.actuator.** { *; }
-keep class io.micrometer.** { *; }

# 日志相关
-keep class org.slf4j.** { *; }
-keep class ch.qos.logback.** { *; }
-keep class org.apache.logging.log4j.** { *; }

#########################################
# 反射支持
#########################################

# 保留所有反射相关的方法
-keepclassmembers class * {
    public <init>(...);
    public void set*(...);
    public *** get*();
    public *** is*();
    public <fields>;
    public <methods>;
}

# 保留所有native方法
-keepclasseswithmembernames class * {
    native <methods>;
}

#########################################
# 开发环境特殊配置
#########################################

# 保留所有内部类
-keep class com.quickdatax.**$* { *; }

# 保留所有泛型信息
-keepattributes Signature

# 保留所有注解信息
-keepattributes *Annotation*,AnnotationDefault

# 保留源文件和行号信息（便于调试）
-keepattributes SourceFile,LineNumberTable

# 保留异常信息
-keepattributes Exceptions

# 保留资源文件
-keepdirectories
-keep class **.R
-keep class **.R$*

# 保留Spring Boot配置文件相关
-keep class org.springframework.boot.env.** { *; }
-keep class org.springframework.core.io.** { *; }

# 保留反射相关的关键类
-keep class java.lang.reflect.** { *; }
-keep class sun.reflect.** { *; }

# 保留类加载器相关
-keep class java.lang.ClassLoader { *; }
-keep class java.net.URLClassLoader { *; }

# 生成详细的映射文件
-printmapping build/proguard/mapping-dev.txt
-printseeds build/proguard/seeds-dev.txt
-printusage build/proguard/usage-dev.txt

# 输出详细信息
-verbose

#########################################
# 调试辅助
#########################################

# 不混淆类名（便于调试）
# -dontobfuscate

# 保留所有方法名（便于调试）
# -keepclassmembernames class * {
#     <methods>;
# }

# 保留所有字段名（便于调试）
# -keepclassmembernames class * {
#     <fields>;
# }
