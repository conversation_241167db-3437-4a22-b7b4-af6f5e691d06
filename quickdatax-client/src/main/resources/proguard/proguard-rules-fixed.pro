#########################################
# QuickDataX Spring Boot Fat JAR 修复配置
# 专门解决 "Failed to get nested archive" 问题
#########################################

#########################################
# 基础配置
#########################################

-dontwarn
-ignorewarnings
-dontoptimize         # 禁用优化，避免字节码验证问题
-dontpreverify        # 禁用预验证
-dontshrink           # 禁用代码压缩
-dontobfuscate        # 临时禁用混淆，先确保能运行

# 保留所有属性和元数据
-keepattributes *

#########################################
# Spring Boot Fat JAR 结构保护（关键！）
#########################################

# 保留JAR文件的目录结构
-keepdirectories
-keepdirectories **

# 保留MANIFEST.MF文件
-keepattributes Manifest

# 保留所有资源文件
-keepclasseswithmembers class * {
    public static void main(java.lang.String[]);
}

# 不要处理内嵌的JAR文件
-dontnote
-dontnote **

#########################################
# Spring Boot Fat JAR 核心保护
#########################################

# 保留Spring Boot Loader（关键！）
-keep class org.springframework.boot.loader.** { *; }
-keep class org.springframework.boot.loader.archive.** { *; }
-keep class org.springframework.boot.loader.jar.** { *; }
-keep class org.springframework.boot.loader.util.** { *; }
-keep class org.springframework.boot.loader.data.** { *; }

# 保留启动类和main方法
-keep class com.quickdatax.client.QuickdataxClientApplication {
    public static void main(java.lang.String[]);
    *;
}

# 保留所有main方法
-keepclasseswithmembers public class * {
    public static void main(java.lang.String[]);
}

# 保留MANIFEST.MF信息
-keepattributes Manifest

#########################################
# Spring 框架完整保护
#########################################

# Spring核心
-keep class org.springframework.** { *; }
-keep interface org.springframework.** { *; }

# Spring Boot自动配置
-keep class org.springframework.boot.** { *; }
-keep class org.springframework.boot.autoconfigure.** { *; }
-keep class org.springframework.boot.context.** { *; }
-keep class org.springframework.boot.env.** { *; }

# 保留所有Spring注解
-keep @interface org.springframework.**
-keep @org.springframework.** class * { *; }

# 保留Spring组件
-keep @org.springframework.stereotype.Component class * { *; }
-keep @org.springframework.stereotype.Service class * { *; }
-keep @org.springframework.stereotype.Repository class * { *; }
-keep @org.springframework.stereotype.Controller class * { *; }
-keep @org.springframework.web.bind.annotation.RestController class * { *; }
-keep @org.springframework.context.annotation.Configuration class * { *; }

# 保留Spring注解的方法和字段
-keepclassmembers class * {
    @org.springframework.** *;
}

#########################################
# 项目代码保护
#########################################

# 保留所有项目代码
-keep class com.quickdatax.** { *; }
-keep interface com.quickdatax.** { *; }
-keep enum com.quickdatax.** { *; }

#########################################
# 数据库相关保护
#########################################

# MyBatis
-keep class org.apache.ibatis.** { *; }
-keep class com.baomidou.mybatisplus.** { *; }
-keep interface com.quickdatax.**.mapper.** { *; }
-keep class com.quickdatax.**.dal.** { *; }

# 数据源
-keep class com.alibaba.druid.** { *; }
-keep class com.baomidou.dynamic.datasource.** { *; }

#########################################
# JSON序列化保护
#########################################

# Jackson
-keep class com.fasterxml.jackson.** { *; }
-keepclassmembers class * {
    @com.fasterxml.jackson.annotation.* *;
}

# FastJSON
-keep class com.alibaba.fastjson.** { *; }

#########################################
# 第三方库保护
#########################################

# Lombok
-keep class lombok.** { *; }
-keep @lombok.** class * { *; }

# 工具库
-keep class cn.hutool.** { *; }
-keep class org.apache.commons.** { *; }

# 验证框架
-keep class javax.validation.** { *; }
-keep class jakarta.validation.** { *; }
-keep class org.hibernate.validator.** { *; }

# Redis
-keep class org.springframework.data.redis.** { *; }
-keep class org.redisson.** { *; }

# 日志
-keep class org.slf4j.** { *; }
-keep class ch.qos.logback.** { *; }

#########################################
# JVM和反射保护
#########################################

# 保留反射相关
-keep class java.lang.reflect.** { *; }
-keep class sun.reflect.** { *; }

# 保留类加载器
-keep class java.lang.ClassLoader { *; }
-keep class java.net.URLClassLoader { *; }

# 保留序列化
-keep class * implements java.io.Serializable { *; }
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# 保留枚举
-keepclassmembers enum * { *; }

# 保留异常类
-keep class * extends java.lang.Exception { *; }
-keep class * extends java.lang.RuntimeException { *; }

#########################################
# 反射方法保护
#########################################

-keepclassmembers class * {
    public <init>(...);
    public void set*(...);
    public *** get*();
    public *** is*();
    public <fields>;
    public <methods>;
}

# 保留native方法
-keepclasseswithmembernames class * {
    native <methods>;
}

#########################################
# 资源文件保护
#########################################

# 保留目录结构
-keepdirectories

# 保留资源文件
-keep class **.R
-keep class **.R$*

#########################################
# 调试和输出
#########################################

# 生成映射文件
-printmapping ${project.build.directory}/proguard/mapping-fixed.txt
-printseeds ${project.build.directory}/proguard/seeds-fixed.txt
-printusage ${project.build.directory}/proguard/usage-fixed.txt

# 详细输出
-verbose
