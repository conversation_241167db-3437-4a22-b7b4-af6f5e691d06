#########################################
# QuickDataX Spring Boot Fat JAR 专用配置
# 解决 "Failed to get nested archive" 问题
#########################################

#########################################
# 基础配置 - 保护Spring Boot结构
#########################################

-dontwarn
-ignorewarnings
-dontoptimize
-dontpreverify
-dontshrink
-dontobfuscate

# 保留所有属性
-keepattributes *

# 保留目录结构（关键！）
-keepdirectories
-keepdirectories **

# 不处理内嵌JAR
-dontnote
-dontnote **

#########################################
# Spring Boot Loader 完全保护
#########################################

# 保留Spring Boot Loader的所有类
-keep class org.springframework.boot.loader.** { *; }
-keep interface org.springframework.boot.loader.** { *; }

# 保留Spring Boot Launcher
-keep class org.springframework.boot.loader.Launcher { *; }
-keep class org.springframework.boot.loader.JarLauncher { *; }
-keep class org.springframework.boot.loader.WarLauncher { *; }
-keep class org.springframework.boot.loader.PropertiesLauncher { *; }

# 保留Archive相关类
-keep class org.springframework.boot.loader.archive.** { *; }
-keep class org.springframework.boot.loader.jar.** { *; }
-keep class org.springframework.boot.loader.util.** { *; }
-keep class org.springframework.boot.loader.data.** { *; }

#########################################
# 启动类和Main方法保护
#########################################

# 保留启动类
-keep class com.quickdatax.client.QuickdataxClientApplication {
    public static void main(java.lang.String[]);
    *;
}

# 保留所有main方法
-keepclasseswithmembers public class * {
    public static void main(java.lang.String[]);
}

#########################################
# MANIFEST.MF 和资源文件保护
#########################################

# 保留MANIFEST.MF
-keepattributes Manifest

# 保留所有资源文件
-keep class **.R
-keep class **.R$*

# 保留Spring Boot配置文件
-keep class org.springframework.boot.env.** { *; }
-keep class org.springframework.core.io.** { *; }

#########################################
# Spring 框架核心保护
#########################################

# Spring核心
-keep class org.springframework.** { *; }
-keep interface org.springframework.** { *; }

# Spring Boot
-keep class org.springframework.boot.** { *; }
-keep interface org.springframework.boot.** { *; }

# Spring注解
-keep @interface org.springframework.**
-keep @org.springframework.** class * { *; }

#########################################
# 项目代码完全保护
#########################################

# 保留所有项目代码
-keep class com.quickdatax.** { *; }
-keep interface com.quickdatax.** { *; }
-keep enum com.quickdatax.** { *; }

#########################################
# 第三方库保护
#########################################

# MyBatis
-keep class org.apache.ibatis.** { *; }
-keep class com.baomidou.mybatisplus.** { *; }

# Jackson
-keep class com.fasterxml.jackson.** { *; }

# Lombok
-keep class lombok.** { *; }

# 工具库
-keep class cn.hutool.** { *; }
-keep class org.apache.commons.** { *; }

# 日志
-keep class org.slf4j.** { *; }
-keep class ch.qos.logback.** { *; }

#########################################
# JVM核心类保护
#########################################

# 反射相关
-keep class java.lang.reflect.** { *; }
-keep class sun.reflect.** { *; }

# 类加载器
-keep class java.lang.ClassLoader { *; }
-keep class java.net.URLClassLoader { *; }

# 序列化
-keep class * implements java.io.Serializable { *; }
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

#########################################
# 枚举和异常保护
#########################################

# 枚举
-keepclassmembers enum * { *; }

# 异常
-keep class * extends java.lang.Exception { *; }
-keep class * extends java.lang.RuntimeException { *; }

#########################################
# 反射方法保护
#########################################

-keepclassmembers class * {
    public <init>(...);
    public void set*(...);
    public *** get*();
    public *** is*();
    public <fields>;
    public <methods>;
}

# Native方法
-keepclasseswithmembernames class * {
    native <methods>;
}

#########################################
# 输出和调试
#########################################

# 生成映射文件
-printmapping ${project.build.directory}/proguard/mapping-springboot.txt
-printseeds ${project.build.directory}/proguard/seeds-springboot.txt
-printusage ${project.build.directory}/proguard/usage-springboot.txt

# 详细输出
-verbose
